--[[
GPIO Module for Robot Library
Advanced GPIO control with interrupt support and pin mapping
]]--

local gpio = {}
local robot = require("robot")

-- Pin mapping for different platforms
local pin_maps = {
    raspberry_pi = {
        -- BCM pin numbering
        [3] = 2,   -- SDA
        [5] = 3,   -- SCL
        [7] = 4,   -- GPIO4
        [8] = 14,  -- TXD
        [10] = 15, -- RXD
        [11] = 17, -- GPIO17
        [12] = 18, -- GPIO18
        [13] = 27, -- GPIO27
        [15] = 22, -- GPIO22
        [16] = 23, -- GPIO23
        [18] = 24, -- GPIO24
        [19] = 10, -- MOSI
        [21] = 9,  -- MISO
        [22] = 25, -- GPIO25
        [23] = 11, -- <PERSON>L<PERSON>
        [24] = 8,  -- CE0
        [26] = 7,  -- CE1
    },
    arduino_uno = {
        -- Digital pins 0-13, Analog pins A0-A5 (14-19)
        [0] = 0, [1] = 1, [2] = 2, [3] = 3, [4] = 4, [5] = 5,
        [6] = 6, [7] = 7, [8] = 8, [9] = 9, [10] = 10, [11] = 11,
        [12] = 12, [13] = 13, [14] = 14, [15] = 15, [16] = 16,
        [17] = 17, [18] = 18, [19] = 19
    }
}

-- Interrupt callbacks
local _interrupt_callbacks = {}

-- PWM configuration
local _pwm_config = {
    frequency = 1000, -- Default 1kHz
    resolution = 8    -- 8-bit resolution (0-255)
}

-- Initialize GPIO module
function gpio.init(platform)
    platform = platform or "simulation"
    gpio.platform = platform
    
    if platform == "raspberry_pi" then
        gpio.pin_map = pin_maps.raspberry_pi
    elseif platform == "arduino_uno" then
        gpio.pin_map = pin_maps.arduino_uno
    else
        gpio.pin_map = {}
    end
    
    print("GPIO module initialized for platform: " .. platform)
end

-- Map logical pin to physical pin
function gpio.map_pin(logical_pin)
    if gpio.pin_map and gpio.pin_map[logical_pin] then
        return gpio.pin_map[logical_pin]
    end
    return logical_pin
end

-- Advanced pin mode with additional options
function gpio.set_pin_mode(pin, mode, options)
    options = options or {}
    
    local physical_pin = gpio.map_pin(pin)
    robot.pin_mode(physical_pin, mode)
    
    -- Handle additional options
    if options.pull_up then
        -- Enable internal pull-up resistor
        if robot.simulation_mode then
            print(string.format("Pin %d: Pull-up resistor enabled", pin))
        end
    end
    
    if options.pull_down then
        -- Enable internal pull-down resistor
        if robot.simulation_mode then
            print(string.format("Pin %d: Pull-down resistor enabled", pin))
        end
    end
    
    if options.drive_strength then
        -- Set drive strength (platform dependent)
        if robot.simulation_mode then
            print(string.format("Pin %d: Drive strength set to %s", pin, options.drive_strength))
        end
    end
end

-- Digital write with validation
function gpio.digital_write(pin, value)
    local physical_pin = gpio.map_pin(pin)
    robot.digital_write(physical_pin, value)
end

-- Digital read with debouncing
function gpio.digital_read(pin, debounce_ms)
    local physical_pin = gpio.map_pin(pin)
    
    if debounce_ms and debounce_ms > 0 then
        -- Simple debouncing
        local first_read = robot.digital_read(physical_pin)
        robot.delay(debounce_ms)
        local second_read = robot.digital_read(physical_pin)
        
        if first_read == second_read then
            return first_read
        else
            -- If readings differ, take another reading
            robot.delay(debounce_ms)
            return robot.digital_read(physical_pin)
        end
    else
        return robot.digital_read(physical_pin)
    end
end

-- PWM configuration
function gpio.set_pwm_config(frequency, resolution)
    _pwm_config.frequency = frequency or _pwm_config.frequency
    _pwm_config.resolution = resolution or _pwm_config.resolution
    
    if robot.simulation_mode then
        print(string.format("PWM config: %d Hz, %d-bit resolution", 
              _pwm_config.frequency, _pwm_config.resolution))
    end
end

-- Advanced analog write with frequency control
function gpio.analog_write(pin, value, frequency)
    local physical_pin = gpio.map_pin(pin)
    
    if frequency and frequency ~= _pwm_config.frequency then
        gpio.set_pwm_config(frequency)
    end
    
    -- Convert value based on resolution
    local max_value = (2 ^ _pwm_config.resolution) - 1
    if value > max_value then
        value = max_value
    end
    
    robot.analog_write(physical_pin, value)
end

-- Analog read with averaging
function gpio.analog_read(pin, samples)
    local physical_pin = gpio.map_pin(pin)
    samples = samples or 1
    
    if samples == 1 then
        return robot.analog_read(physical_pin)
    else
        local total = 0
        for i = 1, samples do
            total = total + robot.analog_read(physical_pin)
            if i < samples then
                robot.delay(1) -- Small delay between samples
            end
        end
        return math.floor(total / samples)
    end
end

-- Interrupt handling (simulation)
function gpio.attach_interrupt(pin, callback, mode)
    mode = mode or "CHANGE"
    local physical_pin = gpio.map_pin(pin)
    
    _interrupt_callbacks[physical_pin] = {
        callback = callback,
        mode = mode,
        last_value = robot.digital_read(physical_pin)
    }
    
    if robot.simulation_mode then
        print(string.format("Interrupt attached to pin %d (mode: %s)", pin, mode))
    end
end

-- Detach interrupt
function gpio.detach_interrupt(pin)
    local physical_pin = gpio.map_pin(pin)
    _interrupt_callbacks[physical_pin] = nil
    
    if robot.simulation_mode then
        print(string.format("Interrupt detached from pin %d", pin))
    end
end

-- Process interrupts (call this in main loop for simulation)
function gpio.process_interrupts()
    for pin, interrupt in pairs(_interrupt_callbacks) do
        local current_value = robot.digital_read(pin)
        local should_trigger = false
        
        if interrupt.mode == "CHANGE" and current_value ~= interrupt.last_value then
            should_trigger = true
        elseif interrupt.mode == "RISING" and current_value == robot.HIGH and interrupt.last_value == robot.LOW then
            should_trigger = true
        elseif interrupt.mode == "FALLING" and current_value == robot.LOW and interrupt.last_value == robot.HIGH then
            should_trigger = true
        end
        
        if should_trigger then
            interrupt.callback(pin, current_value)
        end
        
        interrupt.last_value = current_value
    end
end

-- Pulse measurement
function gpio.pulse_in(pin, value, timeout_us)
    timeout_us = timeout_us or 1000000 -- 1 second default
    local physical_pin = gpio.map_pin(pin)
    local start_time = robot.micros()
    
    -- Wait for pulse to start
    while robot.digital_read(physical_pin) ~= value do
        if robot.micros() - start_time > timeout_us then
            return 0 -- Timeout
        end
    end
    
    -- Measure pulse duration
    local pulse_start = robot.micros()
    while robot.digital_read(physical_pin) == value do
        if robot.micros() - start_time > timeout_us then
            return 0 -- Timeout
        end
    end
    
    return robot.micros() - pulse_start
end

-- Shift operations (for shift registers)
function gpio.shift_out(data_pin, clock_pin, bit_order, value)
    local physical_data_pin = gpio.map_pin(data_pin)
    local physical_clock_pin = gpio.map_pin(clock_pin)
    
    for i = 0, 7 do
        local bit_pos = bit_order == "MSBFIRST" and (7 - i) or i
        local bit_value = (value >> bit_pos) & 1
        
        robot.digital_write(physical_data_pin, bit_value)
        robot.digital_write(physical_clock_pin, robot.HIGH)
        robot.delay_microseconds(1)
        robot.digital_write(physical_clock_pin, robot.LOW)
        robot.delay_microseconds(1)
    end
end

function gpio.shift_in(data_pin, clock_pin, bit_order)
    local physical_data_pin = gpio.map_pin(data_pin)
    local physical_clock_pin = gpio.map_pin(clock_pin)
    local value = 0
    
    for i = 0, 7 do
        robot.digital_write(physical_clock_pin, robot.HIGH)
        robot.delay_microseconds(1)
        
        local bit_value = robot.digital_read(physical_data_pin)
        local bit_pos = bit_order == "MSBFIRST" and (7 - i) or i
        
        if bit_value == robot.HIGH then
            value = value | (1 << bit_pos)
        end
        
        robot.digital_write(physical_clock_pin, robot.LOW)
        robot.delay_microseconds(1)
    end
    
    return value
end

-- Pin state monitoring
function gpio.monitor_pins(pins, duration_ms, callback)
    local start_time = robot.millis()
    local pin_states = {}
    
    -- Initialize pin states
    for _, pin in ipairs(pins) do
        pin_states[pin] = robot.digital_read(gpio.map_pin(pin))
    end
    
    while robot.millis() - start_time < duration_ms do
        for _, pin in ipairs(pins) do
            local current_state = robot.digital_read(gpio.map_pin(pin))
            if current_state ~= pin_states[pin] then
                callback(pin, pin_states[pin], current_state)
                pin_states[pin] = current_state
            end
        end
        robot.delay(1) -- Small delay to prevent excessive CPU usage
    end
end

return gpio

--[[
Motor Control Module for Robot Library
Self-contained motor control without external dependencies
]]--

local motors = {}
local robot = require("robot")

-- Motor types
motors.DC_MOTOR = "dc"
motors.SERVO_MOTOR = "servo"
motors.STEPPER_MOTOR = "stepper"

-- Motor direction constants
motors.FORWARD = 1
motors.BACKWARD = -1
motors.STOP = 0

-- DC Motor Class
local DCMotor = {}
DCMotor.__index = DCMotor

function DCMotor:new(pin1, pin2, enable_pin)
    local motor = {
        pin1 = pin1,
        pin2 = pin2,
        enable_pin = enable_pin,
        speed = 0,
        direction = motors.STOP
    }
    setmetatable(motor, DCMotor)
    
    -- Initialize pins
    robot.pin_mode(pin1, robot.OUTPUT)
    robot.pin_mode(pin2, robot.OUTPUT)
    if enable_pin then
        robot.pin_mode(enable_pin, robot.OUTPUT)
    end
    
    motor:stop()
    return motor
end

function DCMotor:set_speed(speed)
    speed = math.max(-255, math.min(255, speed))
    self.speed = math.abs(speed)
    
    if speed > 0 then
        self.direction = motors.FORWARD
    elseif speed < 0 then
        self.direction = motors.BACKWARD
    else
        self.direction = motors.STOP
    end
    
    self:_update_pins()
end

function DCMotor:forward(speed)
    speed = speed or 255
    self:set_speed(math.abs(speed))
end

function DCMotor:backward(speed)
    speed = speed or 255
    self:set_speed(-math.abs(speed))
end

function DCMotor:stop()
    self.speed = 0
    self.direction = motors.STOP
    self:_update_pins()
end

function DCMotor:_update_pins()
    if self.direction == motors.FORWARD then
        robot.digital_write(self.pin1, robot.HIGH)
        robot.digital_write(self.pin2, robot.LOW)
    elseif self.direction == motors.BACKWARD then
        robot.digital_write(self.pin1, robot.LOW)
        robot.digital_write(self.pin2, robot.HIGH)
    else
        robot.digital_write(self.pin1, robot.LOW)
        robot.digital_write(self.pin2, robot.LOW)
    end
    
    if self.enable_pin then
        robot.analog_write(self.enable_pin, self.speed)
    end
end

-- Servo Motor Class
local ServoMotor = {}
ServoMotor.__index = ServoMotor

function ServoMotor:new(pin, min_pulse, max_pulse)
    local servo = {
        pin = pin,
        min_pulse = min_pulse or 1000,  -- 1ms
        max_pulse = max_pulse or 2000,  -- 2ms
        current_angle = 90,
        pulse_width = 1500  -- 1.5ms (center position)
    }
    setmetatable(servo, ServoMotor)
    
    robot.pin_mode(pin, robot.OUTPUT)
    servo:write(90)  -- Center position
    return servo
end

function ServoMotor:write(angle)
    angle = math.max(0, math.min(180, angle))
    self.current_angle = angle
    
    -- Calculate pulse width based on angle
    local pulse_range = self.max_pulse - self.min_pulse
    self.pulse_width = self.min_pulse + (angle / 180) * pulse_range
    
    self:_generate_pwm()
end

function ServoMotor:read()
    return self.current_angle
end

function ServoMotor:_generate_pwm()
    -- Generate PWM signal for servo (20ms period)
    local period = 20000  -- 20ms in microseconds
    local high_time = self.pulse_width
    local low_time = period - high_time
    
    -- In a real implementation, this would be handled by hardware PWM
    -- For simulation, we'll just set the analog value
    local pwm_value = math.floor((high_time / period) * 255)
    robot.analog_write(self.pin, pwm_value)
    
    if robot.simulation_mode then
        print(string.format("Servo on pin %d: angle=%d°, pulse=%dus", 
              self.pin, self.current_angle, self.pulse_width))
    end
end

-- Stepper Motor Class
local StepperMotor = {}
StepperMotor.__index = StepperMotor

function StepperMotor:new(steps_per_rev, pin1, pin2, pin3, pin4)
    local stepper = {
        steps_per_rev = steps_per_rev,
        pins = {pin1, pin2, pin3, pin4},
        current_step = 0,
        step_delay = 1000,  -- microseconds
        direction = 1
    }
    setmetatable(stepper, StepperMotor)
    
    -- Initialize pins
    for _, pin in ipairs(stepper.pins) do
        robot.pin_mode(pin, robot.OUTPUT)
        robot.digital_write(pin, robot.LOW)
    end
    
    return stepper
end

function StepperMotor:set_speed(rpm)
    -- Calculate step delay based on RPM
    local steps_per_minute = self.steps_per_rev * rpm
    local steps_per_second = steps_per_minute / 60
    self.step_delay = math.floor(1000000 / steps_per_second)  -- microseconds
end

function StepperMotor:step(steps)
    local abs_steps = math.abs(steps)
    self.direction = steps >= 0 and 1 or -1
    
    for i = 1, abs_steps do
        self:_single_step()
        robot.delay_microseconds(self.step_delay)
    end
end

function StepperMotor:_single_step()
    -- 4-step sequence for unipolar stepper
    local step_sequence = {
        {1, 0, 0, 0},
        {0, 1, 0, 0},
        {0, 0, 1, 0},
        {0, 0, 0, 1}
    }
    
    self.current_step = self.current_step + self.direction
    if self.current_step >= 4 then
        self.current_step = 0
    elseif self.current_step < 0 then
        self.current_step = 3
    end
    
    local pattern = step_sequence[self.current_step + 1]
    for i, pin in ipairs(self.pins) do
        robot.digital_write(pin, pattern[i])
    end
    
    if robot.simulation_mode then
        print(string.format("Stepper step: %d, pattern: %s", 
              self.current_step, table.concat(pattern, "")))
    end
end

-- Motor Controller Class (for multiple motors)
local MotorController = {}
MotorController.__index = MotorController

function MotorController:new()
    local controller = {
        motors = {}
    }
    setmetatable(controller, MotorController)
    return controller
end

function MotorController:add_motor(name, motor)
    self.motors[name] = motor
end

function MotorController:get_motor(name)
    return self.motors[name]
end

function MotorController:stop_all()
    for name, motor in pairs(self.motors) do
        if motor.stop then
            motor:stop()
        end
    end
end

function MotorController:move_robot(left_speed, right_speed, duration_ms)
    local left_motor = self.motors.left
    local right_motor = self.motors.right
    
    if not left_motor or not right_motor then
        error("Left and right motors must be defined for robot movement")
    end
    
    left_motor:set_speed(left_speed)
    right_motor:set_speed(right_speed)
    
    if duration_ms then
        robot.delay(duration_ms)
        self:stop_all()
    end
end

function MotorController:turn_left(speed, duration_ms)
    speed = speed or 128
    self:move_robot(-speed, speed, duration_ms)
end

function MotorController:turn_right(speed, duration_ms)
    speed = speed or 128
    self:move_robot(speed, -speed, duration_ms)
end

function MotorController:move_forward(speed, duration_ms)
    speed = speed or 128
    self:move_robot(speed, speed, duration_ms)
end

function MotorController:move_backward(speed, duration_ms)
    speed = speed or 128
    self:move_robot(-speed, -speed, duration_ms)
end

-- Factory functions
function motors.create_dc_motor(pin1, pin2, enable_pin)
    return DCMotor:new(pin1, pin2, enable_pin)
end

function motors.create_servo_motor(pin, min_pulse, max_pulse)
    return ServoMotor:new(pin, min_pulse, max_pulse)
end

function motors.create_stepper_motor(steps_per_rev, pin1, pin2, pin3, pin4)
    return StepperMotor:new(steps_per_rev, pin1, pin2, pin3, pin4)
end

function motors.create_controller()
    return MotorController:new()
end

return motors
